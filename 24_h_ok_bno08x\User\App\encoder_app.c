#include "encoder_app.h"

extern UART_HandleTypeDef huart1;

// ���ұ��������
Encoder left_encoder;
Encoder right_encoder;

/**
 * @brief ��ʼ��������Ӧ��
 */
void Encoder_Init(void)
{
  // 编码器方向设置：0-正常，1-反转
  // 根据实际安装情况调整，确保编码器反馈方向与电机转动方向一致
  Encoder_Driver_Init(&left_encoder, &htim3, 0);
  Encoder_Driver_Init(&right_encoder, &htim4, 0);
}

/**
 * @brief ������Ӧ���������� (Ӧ�ɵ����������Ե���)
 */
void Encoder_Task(void)
{
  Encoder_Driver_Update(&left_encoder);
  Encoder_Driver_Update(&right_encoder);
//	my_printf(&huart1,"%.2f,%.2f\r\n",left_encoder.speed_cm_s,right_encoder.speed_cm_s);
//	my_printf(&huart1,"Left:%.2f Right:%.2f\r\n",left_encoder.speed_cm_s,right_encoder.speed_cm_s);
//	my_printf(&huart1,"Left:%d Right:%d\r\n",left_encoder.total_count,right_encoder.total_count);
}
